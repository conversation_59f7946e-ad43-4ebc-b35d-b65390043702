[package]
name = "dust"
version = "0.1.0"
edition = "2021"

# SERVICES

[[bin]]
name = "core-api"
path = "bin/core_api.rs"

[[bin]]
name = "oauth"
path = "bin/oauth.rs"

[[bin]]
name = "sqlite-worker"
path = "bin/sqlite_worker.rs"

# UTILS

[[bin]]
name = "init_db"
path = "bin/init_db.rs"

[[bin]]
name = "elasticsearch_create_index"
path = "bin/elasticsearch/create_index.rs"

[[bin]]
name = "elasticsearch_backfill_index"
path = "bin/elasticsearch/backfill_index.rs"

[[bin]]
name = "elasticsearch_backfill_folders_index"
path = "bin/elasticsearch/backfill_folders_index.rs"

[[bin]]
name = "qdrant_create_collection"
path = "bin/qdrant/create_collection.rs"

[[bin]]
name = "qdrant_shard_rebalance_suggestions"
path = "bin/qdrant/qdrant_shard_rebalance_suggestions.rs"

# [[bin]]
# name = "qdrant_migrator"
# path = "bin/qdrant/migrator.rs"

# [[bin]]
# name = "qdrant_migrate_embedder"
# path = "bin/qdrant/migrate_embedder.rs"

[[bin]]
name = "qdrant_delete_orphaned_points"
path = "bin/qdrant/delete_orphaned_points.rs"

# [[bin]]
# name = "oauth_generate_key"
# path = "bin/oauth_generate_key.rs"

[[bin]]
name = "salesforce"
path = "bin/salesforce.rs"

# MIGRATIONS

[[bin]]
name = "create_nodes"
path = "bin/migrations/20241204_create_nodes.rs"

[[bin]]
name = "fix_created_dsdocs"
path = "bin/migrations/20241203_fix_created_dsdocs.rs"

[[bin]]
name = "elasticsearch_backfill_document_tags_index"
path = "bin/migrations/20250205_backfill_document_tags_index.rs"

[[bin]]
name = "backfill_elasticsearch_text_size"
path = "bin/migrations/20250226_backfill_elasticsearch_text_size.rs"

[[test]]
name = "oauth_connections_test"
path = "src/oauth/tests/functional_connections.rs"

[[test]]
name = "oauth_credentials_test"
path = "src/oauth/tests/functional_credentials.rs"

[dependencies]
anyhow = "1.0"
serde = { version = "1.0", features = ["rc", "derive"] }
serde_json = "1.0"
pest = "2.7"
pest_derive = "2.7"
shellexpand = "2.1"
blake3 = "1.3"
async-trait = "0.1"
tokio = { version = "1.38", features = ["full"] }
tokio-stream = "0.1"
tokio-util = { version = "0.7", features = ["compat"] }
hyper = { version = "1.3.1", features = ["full"] }
itertools = "0.10"
futures = "0.3"
async-std = "1.12"
lazy_static = "1.4"
regex = "1.10"
rand = "0.8"
uuid = { version = "1.8", features = ["v4"] }
parking_lot = "0.12"
axum = "0.7.4"
rusqlite = { version = "0.31", features = ["bundled"] }
tokio-postgres = { version = "0.7", features = ["with-serde_json-1"] }
bb8 = "0.8"
bb8-postgres = "0.8"
urlencoding = "2.1"
url = "2.5"
dns-lookup = "1.0"
async-stream = "0.3"
eventsource-client = { git = "https://github.com/dust-tt/rust-eventsource-client", rev = "148050f8fb9f8abb25ca171aa68ea817277ca4f6" }
tera = "1.20"
fancy-regex = "0.13"
rustc-hash = "1.1"
bstr = "1.9"
base64 = "0.22"
cloud-storage = { version = "0.11", features = ["global-client"] }
qdrant-client = "1.11"
tower-http = {version = "0.5", features = ["full"]}
tracing = "0.1"
tracing-subscriber = { version = "0.3", features = ["env-filter", "json"] }
deno_core = "0.292"
rayon = "1.10"
clap = { version = "4.5", features = ["derive"] }
async-recursion = "1.1"
chrono = "0.4"
thiserror = "1.0.57"
sentencepiece = { version = "0.11", features = ["static"] }
reqwest = { version = "0.12", features = ["json"] }
tracing-bunyan-formatter = "0.3.9"
http = "1.1.0"
sqids = "0.4.1"
ring = "0.17.14"
jsonwebtoken = "9.3.0"
rslock = { version = "0.4.0", default-features = false, features = ["tokio-comp"] }
snowflake-connector-rs = { git = "https://github.com/dust-tt/snowflake-connector-rs", rev = "5a16356" }
gcp-bigquery-client = "0.25.1"
axum-test = "16.0.0"
csv-async = "1.3.0"
csv = "1.3.0"
tikv-jemallocator = "0.6"
elasticsearch = "8.15.0-alpha.1"
elasticsearch-dsl = "0.4"
unicode-normalization = "0.1.24"
dateparser = "0.2.1"
once_cell = "1.18"
redis = { version = "0.24.0", features = ["tokio-comp"] }
humantime = "2.2.0"
