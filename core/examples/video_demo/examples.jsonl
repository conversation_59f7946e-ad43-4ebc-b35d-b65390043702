{ "question": "What is 37593 * 67?", "code": "37593 * 67" }
{ "question": "<PERSON>'s ducks lay 16 eggs per day. She eats three for breakfast every morning and bakes muffins for her friends every day with four. She sells the remainder at the farmers' market daily for $2 per fresh duck egg. How much in dollars does she make every day at the farmers' market?", "code": "(16-3-4)*2" }
{ "question": "How many of the integers between 0 and 99 inclusive are divisible by 8?", "code": "let count = 0; for (let i = 0; i <= 99; i++) { if (i % 8 === 0) { count+=1 } }; count" }
{ "question": "A robe takes 2 bolts of blue fiber and half that much white fiber. How many bolts in total does it take?", "code": "2 + 2/2" }
